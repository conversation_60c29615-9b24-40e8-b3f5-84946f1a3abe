import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Modal,
  Tabs,
  Pagination,
  Typography
} from 'antd';
const { TabPane } = Tabs;

import { formatCurrency, formatDate } from '@/lib/utils';
import { useInstances } from '@/contexts/InstanceContext';
import {
  getInstancesFromAPI,
} from '@/services/instanceService';
import { useRequest } from 'ahooks';
import {
  Server,
  Cpu,
  HardDrive,
  Zap,
  Play,
  Square,
  RotateCcw,
  Terminal,
  Monitor,
  Settings,
  Trash2,
  Clock,
  DollarSign,
  Activity,
  Download,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import PageHeader from '@/components/PageHeader';


const { Text } = Typography

// 数据转换函数：将 API 数据转换为组件期望的格式
const convertAPIInstanceToInstance = apiInstance => {
  // 状态映射：API 的数字状态转换为字符串状态
  const getStatusFromNumber = status => {
    switch (status) {
      case 1:
        return 'running';
      case 0:
        return 'stopped';
      case 2:
        return 'pending';
      case 3:
        return 'running'; // 根据文档示例，状态3也表示运行中
      default:
        return 'error';
    }
  };

  // 解析端口映射
  const parsePortsMapping = (portsMapping) => {
    try {
      if (typeof portsMapping === 'string') {
        return JSON.parse(portsMapping);
      }
      return portsMapping || [];
    } catch (error) {
      console.error('解析端口映射失败:', error);
      return [];
    }
  };

  // 解析元数据
  const parseMetaData = (metaData) => {
    try {
      if (typeof metaData === 'string') {
        return JSON.parse(metaData);
      }
      return metaData || {};
    } catch (error) {
      console.error('解析元数据失败:', error);
      return {};
    }
  };

  const portsMapping = parsePortsMapping(apiInstance.portsMapping);
  const metaData = parseMetaData(apiInstance.metaData);

  return {
    id: apiInstance.id.toString(),
    name: apiInstance.name,
    deviceId: apiInstance.id.toString(),
    status: getStatusFromNumber(apiInstance.instanceStatus),
    cpu: `${apiInstance.cpuCores}核 ${apiInstance.cpuCode}`,
    memory: `${apiInstance.memorySize} GB`,
    gpu: apiInstance.gpuCode
      ? `${apiInstance.gpuNum || 1}×${apiInstance.gpuCode}`
      : '无',
    storage: `${apiInstance.diskSize} GB`,
    pricePerHour: 0, // 需要根据实际业务计算
    totalCost: 0, // 需要根据实际业务计算
    startTime: apiInstance.createdAt,
    userId: apiInstance.userId || '1',
    applications: [], // 需要根据模板信息填充
    applicationInfo: apiInstance.imageAddress
      ? {
        id: apiInstance.id.toString(),
        name: apiInstance.imageAddress.split('/').pop() || apiInstance.name,
        icon: '🚀',
        version: 'latest',
        status: getStatusFromNumber(apiInstance.instanceStatus),
        ports: portsMapping.map(p => p.port),
        installTime: apiInstance.createdAt,
        accessUrl: metaData.url ? (Array.isArray(metaData.url) ? metaData.url[0] : metaData.url) : undefined,
      }
      : undefined,
    sshInfo: {
      host: apiInstance.sshAddress || '*************',
      port: 22,
      username: apiInstance.sshLoginUser || 'root',
    },
    instanceUuid: apiInstance.instanceUuid,
    machineId: apiInstance.machineId,
    externalId: apiInstance.externalId,
    imageAddress: apiInstance.imageAddress,
    launchedAt: apiInstance.launchedAt,
    terminatedAt: apiInstance.terminatedAt,
    metaData: metaData,
    portsMapping: portsMapping,
  };
};

const MyInstances = () => {
  const { instances, updateInstance, deleteInstance } = useInstances();
  const [searchParams] = useSearchParams();
  const [selectedInstance, setSelectedInstance] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取实例列表数据
  const {
    data: instancesData,
    loading: instancesLoading,
  } = useRequest(
    () => getInstancesFromAPI({}, currentPage, pageSize),
    {
      refreshDeps: [currentPage, pageSize],
    }
  );



  // 转换并排序的实例列表（最新的在前面）
  const sortedInstances = useMemo(() => {
    if (!instancesData?.records) return [];
    return [...instancesData.records]
      .map(convertAPIInstanceToInstance)
      .sort(
        (a, b) =>
          new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
      );
  }, [instancesData]);

  // 处理从其他页面跳转过来的实例ID参数
  useEffect(() => {
    const instanceId = searchParams.get('id');
    if (instanceId && instancesData?.records) {
      const apiInstance = instancesData.records.find(
        inst => inst.id.toString() === instanceId
      );
      if (apiInstance) {
        setSelectedInstance(convertAPIInstanceToInstance(apiInstance));
      }
    }
  }, [searchParams, instancesData]);



  const handleInstanceAction = (instanceId, action) => {
    const instance = instances.find(inst => inst.id === instanceId);
    if (!instance) return;

    switch (action) {
      case 'start':
        toast.success(`${instance.name} 正在启动...`);
        updateInstance(instanceId, { status: 'running' });
        break;
      case 'stop':
        toast.success(`${instance.name} 正在停止...`);
        updateInstance(instanceId, { status: 'stopped' });
        break;
      case 'restart':
        toast.success(`${instance.name} 正在重启...`);
        updateInstance(instanceId, { status: 'pending' });
        break;
      case 'delete':
        toast.success(`${instance.name} 已删除`);
        deleteInstance(instanceId);
        break;
    }
  };

  const handleSSHConnect = instance => {
    if (instance.sshInfo) {
      const sshCommand = `ssh ${instance.sshInfo.username}@${instance.sshInfo.host} -p ${instance.sshInfo.port}`;
      navigator.clipboard.writeText(sshCommand);
      toast.success('SSH连接命令已复制到剪贴板');
    }
  };

  return (
    <div className="min-h-screen space-y-6">
      <PageHeader title="我的实例" subtitle="管理您的计算实例" >
        <Text type="secondary">共 {instancesData?.total} 个实例</Text>
      </PageHeader>

      {/* 实例列表 */}
      {instancesLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-500">加载中...</p>
        </div>
      ) : sortedInstances.length === 0 ? (
        <div className="text-center py-16">
          <Server className="w-20 h-20 text-gray-300 mx-auto mb-6" />
          <h3 className="text-xl font-medium text-gray-900 mb-3">
            暂无实例
          </h3>
          <p className="text-gray-500 mb-6">
            您还没有租用任何计算实例
          </p>
          <Button>前往算力市场</Button>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {sortedInstances.map(instance => (
              <InstanceCard
                key={instance.id}
                instance={instance}
                onAction={handleInstanceAction}
                onSSHConnect={handleSSHConnect}
                onViewDetails={setSelectedInstance}
              />
            ))}
          </div>

          {/* 分页 */}
          {instancesData && instancesData.total > pageSize && (
            <div className="mt-6 flex justify-center">
              <Pagination
                current={currentPage}
                total={instancesData.total}
                pageSize={pageSize}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={(page, size) => {
                  setCurrentPage(page);
                  if (size !== pageSize) {
                    setPageSize(size);
                  }
                }}
              />
            </div>
          )}
        </>
      )}

      {/* 实例详情对话框 */}
      <InstanceDetailsDialog
        isOpen={!!selectedInstance}
        instance={selectedInstance}
        onClose={() => setSelectedInstance(null)}
      />
    </div>
  );
};

const InstanceCard = ({ instance, onAction, onSSHConnect, onViewDetails }) => {
  const getStatusIcon = status => {
    switch (status) {
      case 'running':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'stopped':
        return <XCircle className="w-4 h-4 text-gray-600" />;
      case 'pending':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />;
    }
  };

  const getStatusText = status => {
    switch (status) {
      case 'running':
        return '运行中';
      case 'stopped':
        return '已停止';
      case 'pending':
        return '启动中';
      case 'error':
        return '错误';
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'running':
        return 'text-green-600 bg-green-50';
      case 'stopped':
        return 'text-gray-600 bg-gray-50';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'error':
        return 'text-red-600 bg-red-50';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow border-none">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {instance.name}
              </h3>
              <div
                className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                  instance.status
                )}`}
              >
                {getStatusIcon(instance.status)}
                {getStatusText(instance.status)}
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Cpu className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">CPU</div>
                  <div className="text-sm font-medium">{instance.cpu}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">内存</div>
                  <div className="text-sm font-medium">{instance.memory}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">GPU</div>
                  <div className="text-sm font-medium">{instance.gpu}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Server className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">存储</div>
                  <div className="text-sm font-medium">{instance.storage}</div>
                </div>
              </div>
            </div>

            {/* 应用信息 */}
            {instance.applicationInfo && (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-2xl">
                    {instance.applicationInfo.icon}
                  </span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-blue-900">
                        {instance.applicationInfo.name}
                      </h4>
                      <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                        v{instance.applicationInfo.version}
                      </span>
                      <div
                        className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${instance.applicationInfo.status === 'running'
                          ? 'text-green-600 bg-green-100'
                          : instance.applicationInfo.status === 'installing'
                            ? 'text-yellow-600 bg-yellow-100'
                            : instance.applicationInfo.status === 'failed'
                              ? 'text-red-600 bg-red-100'
                              : 'text-gray-600 bg-gray-100'
                          }`}
                      >
                        {instance.applicationInfo.status === 'running' && (
                          <CheckCircle className="w-3 h-3" />
                        )}
                        {instance.applicationInfo.status === 'installing' && (
                          <Activity className="w-3 h-3" />
                        )}
                        {instance.applicationInfo.status === 'failed' && (
                          <XCircle className="w-3 h-3" />
                        )}
                        {instance.applicationInfo.status === 'running'
                          ? '运行中'
                          : instance.applicationInfo.status === 'installing'
                            ? '安装中'
                            : instance.applicationInfo.status === 'failed'
                              ? '失败'
                              : '已停止'}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 mt-1 text-xs text-blue-700">
                      <span>
                        端口: {instance.applicationInfo.ports.join(', ')}
                      </span>
                      {instance.applicationInfo.pricePerHour && (
                        <span>
                          应用费用:{' '}
                          {formatCurrency(
                            instance.applicationInfo.pricePerHour
                          )}
                          /小时
                        </span>
                      )}
                      {instance.applicationInfo.installTime && (
                        <span>
                          安装时间:{' '}
                          {formatDate(instance.applicationInfo.installTime)}
                        </span>
                      )}
                    </div>
                  </div>
                  {instance.applicationInfo.accessUrl &&
                    instance.applicationInfo.status === 'running' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const url = instance.applicationInfo?.accessUrl;
                          if (url) {
                            // 如果URL不包含协议，添加https://
                            const fullUrl = url.startsWith('http') ? url : `https://${url}`;
                            window.open(fullUrl, '_blank');
                          }
                        }}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        访问
                      </Button>
                    )}
                </div>
              </div>
            )}

            <div className="flex items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>启动时间: {formatDate(instance.startTime)}</span>
              </div>
              <div className="flex items-center gap-1">
                <DollarSign className="w-4 h-4" />
                <span>费用: {formatCurrency(instance.totalCost)}</span>
              </div>
              <div className="flex items-center gap-1">
                <span>价格: {formatCurrency(instance.pricePerHour)}/小时</span>
                {instance.applicationInfo?.pricePerHour && (
                  <span className="text-xs text-gray-500">
                    (设备{' '}
                    {formatCurrency(
                      instance.pricePerHour -
                      instance.applicationInfo.pricePerHour
                    )}{' '}
                    + 应用{' '}
                    {formatCurrency(instance.applicationInfo.pricePerHour)})
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2 ml-4">
            {instance.status === 'running' ? (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSSHConnect(instance)}
                  className="w-24"
                >
                  <Terminal className="w-4 h-4 mr-1" />
                  SSH
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAction(instance.id, 'stop')}
                  className="w-24"
                >
                  <Square className="w-4 h-4 mr-1" />
                  停止
                </Button>
              </>
            ) : (
              <Button
                size="sm"
                onClick={() => onAction(instance.id, 'start')}
                className="w-24"
              >
                <Play className="w-4 h-4 mr-1" />
                启动
              </Button>
            )}

            <Button
              size="sm"
              variant="outline"
              onClick={() => onViewDetails(instance)}
              className="w-24"
            >
              <Monitor className="w-4 h-4 mr-1" />
              详情
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => onAction(instance.id, 'restart')}
              className="w-24"
            >
              <RotateCcw className="w-4 h-4 mr-1" />
              重启
            </Button>

            <Button
              size="sm"
              variant="destructive"
              onClick={() => onAction(instance.id, 'delete')}
              className="w-24"
            >
              <Trash2 className="w-4 h-4 mr-1" />
              删除
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const InstanceDetailsDialog = ({ isOpen, instance, onClose }) => {
  if (!instance) return null;

  // 使用实例的应用信息而不是 mockApplications
  const applications = instance.applicationInfo ? [instance.applicationInfo] : [];

  return (
    <Modal
      title={<div className="text-2xl font-bold">{instance.name}</div>}
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
      styles={{ body: { maxHeight: '90vh', overflow: 'auto', padding: 24 } }}
    >
      <Tabs defaultActiveKey="overview" className="w-full">
        <TabPane tab="概览" key="overview">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">实例ID</span>
                    <span className="font-medium">{instance.id}</span>
                  </div>
                  {instance.instanceUuid && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">实例UUID</span>
                      <span className="font-medium text-xs">{instance.instanceUuid}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">状态</span>
                    <span className="font-medium">
                      {instance.status === 'running' ? '运行中' :
                        instance.status === 'stopped' ? '已停止' :
                          instance.status === 'pending' ? '启动中' : '错误'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">创建时间</span>
                    <span className="font-medium">
                      {formatDate(instance.startTime)}
                    </span>
                  </div>
                  {instance.launchedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">启动时间</span>
                      <span className="font-medium">
                        {formatDate(instance.launchedAt)}
                      </span>
                    </div>
                  )}
                  {instance.imageAddress && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">镜像地址</span>
                      <span className="font-medium text-xs">{instance.imageAddress}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">价格</span>
                    <span className="font-medium">
                      {formatCurrency(instance.pricePerHour)}/小时
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总费用</span>
                    <span className="font-medium text-orange-600">
                      {formatCurrency(instance.totalCost)}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>硬件配置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">CPU</span>
                    <span className="font-medium">{instance.cpu}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">内存</span>
                    <span className="font-medium">{instance.memory}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GPU</span>
                    <span className="font-medium">{instance.gpu}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">存储</span>
                    <span className="font-medium">{instance.storage}</span>
                  </div>
                  {instance.portsMapping && instance.portsMapping.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">端口映射</span>
                      <span className="font-medium">
                        {instance.portsMapping.map(p => `${p.port}/${p.protocol}`).join(', ')}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {instance.sshInfo && (
              <Card>
                <CardHeader>
                  <CardTitle>SSH连接信息</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg">
                    <code>
                      ssh {instance.sshInfo.username}@{instance.sshInfo.host} -p{' '}
                      {instance.sshInfo.port}
                    </code>
                  </div>
                  <Button
                    className="mt-3"
                    onClick={() => {
                      const command = `ssh ${instance.sshInfo?.username}@${instance.sshInfo?.host} -p ${instance.sshInfo?.port}`;
                      navigator.clipboard.writeText(command);
                      toast.success('SSH命令已复制到剪贴板');
                    }}
                  >
                    复制SSH命令
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabPane>

        <TabPane tab="监控" key="monitoring">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>CPU使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">45%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: '45%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>内存使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">68%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: '68%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>GPU使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">82%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: '82%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>费用统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>今日费用</span>
                    <span className="font-medium">
                      {formatCurrency(instance.pricePerHour * 8)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>本周费用</span>
                    <span className="font-medium">
                      {formatCurrency(instance.pricePerHour * 56)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>本月费用</span>
                    <span className="font-medium">
                      {formatCurrency(instance.totalCost)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabPane>

        <TabPane tab="应用" key="applications">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {applications.length > 0 ? (
                applications.map(app => (
                  <Card key={app.id}>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{app.icon}</div>
                        <div>
                          <CardTitle>{app.name}</CardTitle>
                          <CardDescription>{app.version}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">
                        {app.description}
                      </p>
                      <div className="flex gap-2">
                        {app.accessUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const url = app.accessUrl;
                              if (url) {
                                // 如果URL不包含协议，添加https://
                                const fullUrl = url.startsWith('http') ? url : `https://${url}`;
                                window.open(fullUrl, '_blank');
                              }
                            }}
                          >
                            <ExternalLink className="w-4 h-4 mr-1" />
                            访问
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          <Settings className="w-4 h-4 mr-1" />
                          配置
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-2 text-center py-8">
                  <Download className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    暂无应用
                  </h3>
                  <p className="text-gray-500 mb-4">您还没有安装任何应用</p>
                  <Button>前往应用市场</Button>
                </div>
              )}
            </div>
          </div>
        </TabPane>

        <TabPane tab="设置" key="settings">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>实例设置</CardTitle>
                <CardDescription>管理您的实例配置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">自动续费</h4>
                    <p className="text-sm text-gray-600">实例到期时自动续费</p>
                  </div>
                  <Button variant="outline" size="sm">
                    开启
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">备份设置</h4>
                    <p className="text-sm text-gray-600">定期备份实例数据</p>
                  </div>
                  <Button variant="outline" size="sm">
                    配置
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">监控告警</h4>
                    <p className="text-sm text-gray-600">资源使用率告警通知</p>
                  </div>
                  <Button variant="outline" size="sm">
                    设置
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">危险操作</CardTitle>
                <CardDescription>以下操作不可逆，请谨慎操作</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">重置实例</h4>
                    <p className="text-sm text-gray-600">
                      清除所有数据并重新初始化
                    </p>
                  </div>
                  <Button variant="destructive" size="sm">
                    重置
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">删除实例</h4>
                    <p className="text-sm text-gray-600">
                      永久删除此实例及所有数据
                    </p>
                  </div>
                  <Button variant="destructive" size="sm">
                    删除
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default MyInstances;
