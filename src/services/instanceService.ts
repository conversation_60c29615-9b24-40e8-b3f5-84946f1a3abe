/**
 * 实例管理服务
 */
import http from '@/utils/http';
import { Instance, mockInstances } from '@/data/mockData';
import {
  mockRequest,
  createPaginatedResponse,
  ApiResponse,
  PaginatedResponse,
} from '@/utils/api';
import { API_DELAY } from '@/utils/constants';

const API_BASE = '/ihmp';

// 接口数据类型定义
export interface InstanceQueryDTO {
  instanceStatus?: number;
  appType?: number;
  keyword?: string;
  createdStart?: string;
  createdEnd?: string;
  userId?: number;
  tenantId?: number;
}

export interface InstanceWithAppTemplateVO {
  instanceId: number;
  instanceName: string;
  instanceUuid: string;
  instanceStatus: number;
  createdAt: string;
  cpuCores: number;
  cpuCode: string;
  memorySize: number;
  gpuCode: string;
  gpuNum: number;
  diskSize: number;
  templateId: number;
  templateName: string;
  templateVersion: string;
  containerImage: string;
}

export interface IPageInstanceWithAppTemplateVO {
  size: number;
  pages: number;
  total: number;
  current: number;
  records: InstanceWithAppTemplateVO[];
}

export interface TenantInstanceStatVO {
  tenantId: string;
  total: number;
  running: number;
  stopped: number;
}

// 获取实例列表 - 对接真实接口
export const getInstancesFromAPI = async (
  queryDTO?: InstanceQueryDTO,
  pageIndex: number = 1,
  pageSize: number = 10
): Promise<any> => {
  const requestBody = {
    pageIndex,
    pageSize,
    ...queryDTO
  };

  return http.post(`${API_BASE}/instance/page`, requestBody);
};

// 获取租户实例统计数据
export const getTenantInstanceStats =
  async (): Promise<TenantInstanceStatVO> => {
    return http.get(`${API_BASE}/app/deploy/stats/tenant`);
  };

// 保留原有的 mock 方法用于兼容
export const getInstances = async (params?: {
  page?: number;
  pageSize?: number;
  userId?: string;
  status?: string;
}): Promise<PaginatedResponse<Instance>> => {
  const { page = 1, pageSize = 10, userId, status } = params || {};

  let filteredInstances = [...mockInstances];

  // 过滤条件
  if (userId) {
    filteredInstances = filteredInstances.filter(
      instance => instance.userId === userId
    );
  }
  if (status) {
    filteredInstances = filteredInstances.filter(
      instance => instance.status === status
    );
  }

  // 模拟网络延时
  await new Promise(resolve => setTimeout(resolve, API_DELAY.NORMAL));

  return createPaginatedResponse(filteredInstances, page, pageSize);
};

// 获取单个实例详情
export const getInstance = async (
  id: string
): Promise<ApiResponse<Instance>> => {
  const instance = mockInstances.find(i => i.id === id);

  if (!instance) {
    throw new Error('实例不存在');
  }

  return mockRequest(instance, { delay: API_DELAY.FAST });
};

// 创建实例
export const createInstance = async (
  instanceData: Omit<Instance, 'id'>
): Promise<ApiResponse<Instance>> => {
  const newInstance: Instance = {
    ...instanceData,
    id: `inst-${Date.now()}`,
  };

  mockInstances.push(newInstance);

  return mockRequest(newInstance, { delay: API_DELAY.SLOW });
};

// 更新实例状态
export const updateInstanceStatus = async (
  id: string,
  status: 'running' | 'stopped' | 'pending' | 'error'
): Promise<ApiResponse<Instance>> => {
  const instanceIndex = mockInstances.findIndex(i => i.id === id);

  if (instanceIndex === -1) {
    throw new Error('实例不存在');
  }

  mockInstances[instanceIndex] = {
    ...mockInstances[instanceIndex],
    status,
  };

  return mockRequest(mockInstances[instanceIndex], { delay: API_DELAY.NORMAL });
};

// 删除实例
export const deleteInstance = async (
  id: string
): Promise<ApiResponse<{ id: string }>> => {
  const instanceIndex = mockInstances.findIndex(i => i.id === id);

  if (instanceIndex === -1) {
    throw new Error('实例不存在');
  }

  mockInstances.splice(instanceIndex, 1);

  return mockRequest({ id }, { delay: API_DELAY.NORMAL });
};

// SSH 连接实例
export const connectSSH = async (
  id: string
): Promise<
  ApiResponse<{
    host: string;
    port: number;
    username: string;
    command: string;
  }>
> => {
  const instance = mockInstances.find(i => i.id === id);

  if (!instance || !instance.sshInfo) {
    throw new Error('实例不存在或SSH信息不可用');
  }

  const sshCommand = `ssh ${instance.sshInfo.username}@${instance.sshInfo.host} -p ${instance.sshInfo.port}`;

  return mockRequest(
    {
      ...instance.sshInfo,
      command: sshCommand,
    },
    { delay: API_DELAY.FAST }
  );
};

// 获取实例日志
export const getInstanceLogs = async (
  id: string
): Promise<ApiResponse<string[]>> => {
  const logs = [
    '[2024-01-20 10:00:00] 实例启动中...',
    '[2024-01-20 10:00:15] 正在分配资源...',
    '[2024-01-20 10:00:30] 资源分配完成',
    '[2024-01-20 10:00:45] 正在启动容器...',
    '[2024-01-20 10:01:00] 容器启动成功',
    '[2024-01-20 10:01:15] 应用正在初始化...',
    '[2024-01-20 10:01:30] 应用启动完成',
    '[2024-01-20 10:01:45] 实例运行正常',
  ];

  return mockRequest(logs, { delay: API_DELAY.FAST });
};
