// 测试实例数据转换函数
// 这个文件用于验证 API 数据转换是否正确

// 模拟 API 响应数据（基于文档示例）
const mockAPIResponse = {
  "serverName": "default",
  "timestamp": "2025-08-06 16:28:51",
  "code": 0,
  "msg": "成功",
  "data": {
    "records": [
      {
        "id": 51,
        "name": "xxxxx-8888",
        "instanceUuid": "4a0208a0-b699-4b17-85a1-eca17aebb7ad",
        "type": "APP",
        "machineId": 1,
        "externalId": 3,
        "imageAddress": "artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23",
        "cpuCores": 1,
        "cpuCode": "i9-11700k",
        "memorySize": 1,
        "gpuCode": "2684",
        "gpuNum": null,
        "diskSize": 0,
        "imageCode": null,
        "resourceInfo": null,
        "sshAddress": null,
        "sshLoginUser": "root",
        "sshLoginPassword": null,
        "portsMapping": "[{\"protocol\": \"TCP\", \"port\": \"9300\"}]",
        "environmentVars": "null",
        "createdAt": "2025-07-29 15:11:16",
        "launchedAt": "2025-08-06 16:28:29",
        "terminatedAt": null,
        "terminatedType": null,
        "terminatedUser": null,
        "instanceStatus": 1,
        "metaData": "{\"url\":[\"xxxxx-8888.conn-infra.sail-cloud.com\"]}",
        "userId": "1",
        "tenantId": "2"
      }
    ],
    "total": 4,
    "size": 10,
    "current": 1,
    "orders": [],
    "optimizeCountSql": true,
    "searchCount": true,
    "maxLimit": null,
    "countId": null,
    "pages": 1
  }
};

// 数据转换函数（从 MyInstances.jsx 复制）
const convertAPIInstanceToInstance = apiInstance => {
  // 状态映射：API 的数字状态转换为字符串状态
  const getStatusFromNumber = status => {
    switch (status) {
      case 1:
        return 'running';
      case 0:
        return 'stopped';
      case 2:
        return 'pending';
      case 3:
        return 'running'; // 根据文档示例，状态3也表示运行中
      default:
        return 'error';
    }
  };

  // 解析端口映射
  const parsePortsMapping = (portsMapping) => {
    try {
      if (typeof portsMapping === 'string') {
        return JSON.parse(portsMapping);
      }
      return portsMapping || [];
    } catch (error) {
      console.error('解析端口映射失败:', error);
      return [];
    }
  };

  // 解析元数据
  const parseMetaData = (metaData) => {
    try {
      if (typeof metaData === 'string') {
        return JSON.parse(metaData);
      }
      return metaData || {};
    } catch (error) {
      console.error('解析元数据失败:', error);
      return {};
    }
  };

  const portsMapping = parsePortsMapping(apiInstance.portsMapping);
  const metaData = parseMetaData(apiInstance.metaData);

  return {
    id: apiInstance.id.toString(),
    name: apiInstance.name,
    deviceId: apiInstance.id.toString(),
    status: getStatusFromNumber(apiInstance.instanceStatus),
    cpu: `${apiInstance.cpuCores}核 ${apiInstance.cpuCode}`,
    memory: `${apiInstance.memorySize} GB`,
    gpu: apiInstance.gpuCode
      ? `${apiInstance.gpuNum || 1}×${apiInstance.gpuCode}`
      : '无',
    storage: `${apiInstance.diskSize} GB`,
    pricePerHour: 0, // 需要根据实际业务计算
    totalCost: 0, // 需要根据实际业务计算
    startTime: apiInstance.createdAt,
    userId: apiInstance.userId || '1',
    applications: [], // 需要根据模板信息填充
    applicationInfo: apiInstance.imageAddress
      ? {
        id: apiInstance.id.toString(),
        name: apiInstance.imageAddress.split('/').pop() || apiInstance.name,
        icon: '🚀',
        version: 'latest',
        status: getStatusFromNumber(apiInstance.instanceStatus),
        ports: portsMapping.map(p => p.port),
        installTime: apiInstance.createdAt,
        accessUrl: metaData.url ? (Array.isArray(metaData.url) ? metaData.url[0] : metaData.url) : undefined,
      }
      : undefined,
    sshInfo: {
      host: apiInstance.sshAddress || '*************',
      port: 22,
      username: apiInstance.sshLoginUser || 'root',
    },
    instanceUuid: apiInstance.instanceUuid,
    machineId: apiInstance.machineId,
    externalId: apiInstance.externalId,
    imageAddress: apiInstance.imageAddress,
    launchedAt: apiInstance.launchedAt,
    terminatedAt: apiInstance.terminatedAt,
    metaData: metaData,
    portsMapping: portsMapping,
  };
};

// 测试转换函数
console.log('测试实例数据转换...');
const testInstance = mockAPIResponse.data.records[0];
const convertedInstance = convertAPIInstanceToInstance(testInstance);

console.log('原始数据:', testInstance);
console.log('转换后数据:', convertedInstance);

// 验证关键字段
console.log('验证结果:');
console.log('- ID:', convertedInstance.id === '51' ? '✓' : '✗');
console.log('- 名称:', convertedInstance.name === 'xxxxx-8888' ? '✓' : '✗');
console.log('- 状态:', convertedInstance.status === 'running' ? '✓' : '✗');
console.log('- CPU:', convertedInstance.cpu === '1核 i9-11700k' ? '✓' : '✗');
console.log('- 内存:', convertedInstance.memory === '1 GB' ? '✓' : '✗');
console.log('- GPU:', convertedInstance.gpu === '1×2684' ? '✓' : '✗');
console.log('- 端口映射:', convertedInstance.portsMapping.length > 0 ? '✓' : '✗');
console.log('- 访问URL:', convertedInstance.applicationInfo?.accessUrl ? '✓' : '✗');

export { convertAPIInstanceToInstance, mockAPIResponse };
