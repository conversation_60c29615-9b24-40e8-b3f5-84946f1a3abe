# 全局公共参数

**全局Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| User-Id | 1 | string | 是 | - |
| User-Name | localUser | string | 是 | - |
| Tenant-Id | 1 | string | 是 | - |
| Tenant-Name | localTenanter | string | 是 | - |
| Group-Id | 1 | string | 是 | - |

**全局Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**全局认证方式**

> 无需认证

# 状态码说明

| 状态码 | 中文描述 |
| --- | ---- |
| 暂无参数 |

# 智能异构撮合

> 创建人: 帕拉斯

> 更新人: 帕拉斯

> 创建时间: 2025-07-23 09:54:11

> 更新时间: 2025-07-23 09:54:11

```text
暂无描述
```

**目录Header参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录Body参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| 暂无参数 |

**目录认证信息**

> 继承父级

**Query**

## 实例分页查询

> 创建人: 帕拉斯

> 更新人: 帕拉斯

> 创建时间: 2025-08-06 16:09:51

> 更新时间: 2025-08-06 16:29:06

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> http://localhost:10001/ihmp/instance/page

**请求方式**

> GET

**Content-Type**

> json

**请求Body参数**

```javascript
{
    "pageIndex":1,
    "pageSize":10
}
```

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
	"serverName": "default",
	"timestamp": "2025-08-06 16:28:51",
	"code": 0,
	"msg": "成功",
	"data": {
		"records": [
			{
				"id": 51,
				"name": "xxxxx-8888",
				"instanceUuid": "4a0208a0-b699-4b17-85a1-eca17aebb7ad",
				"type": "APP",
				"machineId": 1,
				"externalId": 3,
				"imageAddress": "artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23",
				"cpuCores": 1,
				"cpuCode": "i9-11700k",
				"memorySize": 1,
				"gpuCode": "2684",
				"gpuNum": null,
				"diskSize": 0,
				"imageCode": null,
				"resourceInfo": null,
				"sshAddress": null,
				"sshLoginUser": "root",
				"sshLoginPassword": null,
				"portsMapping": "[{\"protocol\": \"TCP\", \"port\": \"9300\"}]",
				"environmentVars": "null",
				"createdAt": "2025-07-29 15:11:16",
				"launchedAt": "2025-08-06 16:28:29",
				"terminatedAt": null,
				"terminatedType": null,
				"terminatedUser": null,
				"instanceStatus": 1,
				"metaData": "{\"url\":[\"xxxxx-8888.conn-infra.sail-cloud.com\"]}",
				"yamlConf": "---\napiVersion: \"apps/v1\"\nkind: \"Deployment\"\nmetadata:\n  annotations:\n    actions: \"1/create/2025-07-29T07:11:16.951808900Z\"\n  labels:\n    is_newly: \"1\"\n    ihmp_instance_uuid: \"4a0208a0-b699-4b17-85a1-eca17aebb7ad\"\n    ihmp_app_name: \"xxxxx-8888\"\n  name: \"xxxxx-8888\"\n  namespace: \"2\"\nspec:\n  replicas: 1\n  revisionHistoryLimit: 100\n  selector:\n    matchLabels:\n      ihmp_app_name: \"xxxxx-8888\"\n  strategy:\n    rollingUpdate:\n      maxSurge: \"25%\"\n      maxUnavailable: \"25%\"\n    type: \"RollingUpdate\"\n  template:\n    metadata:\n      labels:\n        ihmp_app_name: \"xxxxx-8888\"\n        ihmp_instance_uuid: \"4a0208a0-b699-4b17-85a1-eca17aebb7ad\"\n    spec:\n      containers:\n      - args:\n        - \"nginx\"\n        - \"-g\"\n        - null\n        - \"off;&quot;\"\n        image: \"artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23\"\n        imagePullPolicy: \"IfNotPresent\"\n        name: \"xxxxx-8888\"\n        ports:\n        - containerPort: 9300\n        resources:\n          limits:\n            cpu: \"1\"\n            memory: \"1Gi\"\n          requests:\n            cpu: \"1\"\n            memory: \"1Gi\"\n      dnsPolicy: \"ClusterFirst\"\n      nodeName: \"scsp04854\"\n      restartPolicy: \"Always\"\n      schedulerName: \"default-scheduler\"\n\n---\napiVersion: \"v1\"\nkind: \"Service\"\nmetadata:\n  annotations:\n    prometheus.io/scrape: \"true\"\n  labels:\n    ihmp_instance_uuid: \"4a0208a0-b699-4b17-85a1-eca17aebb7ad\"\n    ihmp_app_name: \"xxxxx-8888\"\n  name: \"xxxxx-8888\"\n  namespace: \"2\"\nspec:\n  ports:\n  - name: \"nolnhc\"\n    port: 9300\n    protocol: \"TCP\"\n  selector:\n    ihmp_app_name: \"xxxxx-8888\"\n  type: \"ClusterIP\"\n\n---\napiVersion: \"networking.istio.io/v1alpha3\"\nkind: \"VirtualService\"\nmetadata:\n  creationTimestamp: \"2025-07-29T07:11:18Z\"\n  generation: 1\n  labels:\n    ihmp_app_name: \"xxxxx-8888\"\n    ihmp_instance_uuid: \"4a0208a0-b699-4b17-85a1-eca17aebb7ad\"\n  managedFields:\n  - apiVersion: \"networking.istio.io/v1alpha3\"\n    fieldsType: \"FieldsV1\"\n    fieldsV1:\n      f:metadata:\n        f:labels:\n          .: {}\n          f:ihmp_app_name: {}\n          f:ihmp_instance_uuid: {}\n      f:spec:\n        .: {}\n        f:gateways: {}\n        f:hosts: {}\n        f:http: {}\n    manager: \"fabric8-kubernetes-client\"\n    operation: \"Update\"\n    time: \"2025-07-29T07:11:18Z\"\n  name: \"xxxxx-8888\"\n  namespace: \"2\"\n  resourceVersion: \"250700571\"\n  uid: \"e15368fa-9e61-4c93-9408-1176e0ea5cdb\"\nspec:\n  gateways:\n  - \"edgemesh-gateway\"\n  hosts:\n  - \"xxxxx-8888.conn-infra.sail-cloud.com\"\n  http:\n  - match:\n    - port: 36851\n    route:\n    - destination:\n        host: \"xxxxx-8888\"\n        port:\n          number: 9300\n\n",
				"userId": "1",
				"tenantId": "2",
				"portsMappingJsonParse": [
					{
						"protocol": "TCP",
						"port": "9300"
					}
				],
				"environmentVarsJsonParse": null
			},
			{
				"id": 59,
				"name": "llama2-7b-------3617",
				"instanceUuid": "2361cdd3-0e3d-484f-b3ab-26c01c1ea5fe",
				"type": "APP",
				"machineId": 1,
				"externalId": 16,
				"imageAddress": "huggingface/llama2-7b-chat:latest",
				"cpuCores": 1,
				"cpuCode": "i9-11700k",
				"memorySize": 256,
				"gpuCode": "2684",
				"gpuNum": 1,
				"diskSize": 0,
				"imageCode": null,
				"resourceInfo": null,
				"sshAddress": null,
				"sshLoginUser": "root",
				"sshLoginPassword": null,
				"portsMapping": "[{\"protocol\": \"TCP\", \"port\": \"9300\"}]",
				"environmentVars": "{}",
				"createdAt": "2025-08-04 17:18:53",
				"launchedAt": "2025-08-06 16:28:54",
				"terminatedAt": null,
				"terminatedType": null,
				"terminatedUser": null,
				"instanceStatus": 3,
				"metaData": "{\"port\":[61103],\"url\":\"llama2-7b-------3617.conn-infra.sail-cloud.com\"}",
				"yamlConf": "---\napiVersion: \"apps/v1\"\nkind: \"Deployment\"\nmetadata:\n  annotations:\n    actions: \"1/create/2025-08-04T09:18:53.776581776Z\"\n  labels:\n    is_newly: \"1\"\n    ihmp_instance_uuid: \"2361cdd3-0e3d-484f-b3ab-26c01c1ea5fe\"\n    ihmp_app_name: \"llama2-7b-------3617\"\n  name: \"llama2-7b-------3617\"\n  namespace: \"1\"\nspec:\n  replicas: 1\n  revisionHistoryLimit: 100\n  selector:\n    matchLabels:\n      ihmp_app_name: \"llama2-7b-------3617\"\n  strategy:\n    rollingUpdate:\n      maxSurge: \"25%\"\n      maxUnavailable: \"25%\"\n    type: \"RollingUpdate\"\n  template:\n    metadata:\n      labels:\n        ihmp_app_name: \"llama2-7b-------3617\"\n        ihmp_instance_uuid: \"2361cdd3-0e3d-484f-b3ab-26c01c1ea5fe\"\n    spec:\n      containers:\n      - args:\n        - \"python\"\n        - \"-m\"\n        - \"llama.generation\"\n        - \"--model_path\"\n        - \"/models/llama2-7b\"\n        image: \"huggingface/llama2-7b-chat:latest\"\n        imagePullPolicy: \"IfNotPresent\"\n        name: \"llama2-7b-------3617\"\n        ports:\n        - containerPort: 9300\n        resources:\n          limits:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n          requests:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n      dnsPolicy: \"ClusterFirst\"\n      nodeName: \"scsp04854\"\n      restartPolicy: \"Always\"\n      schedulerName: \"default-scheduler\"\n\n---\napiVersion: \"v1\"\nkind: \"Service\"\nmetadata:\n  annotations:\n    prometheus.io/scrape: \"true\"\n  labels:\n    ihmp_instance_uuid: \"2361cdd3-0e3d-484f-b3ab-26c01c1ea5fe\"\n    ihmp_app_name: \"llama2-7b-------3617\"\n  name: \"llama2-7b-------3617\"\n  namespace: \"1\"\nspec:\n  ports:\n  - name: \"y3f7d4\"\n    port: 9300\n    protocol: \"TCP\"\n  selector:\n    ihmp_app_name: \"llama2-7b-------3617\"\n  type: \"ClusterIP\"\n\n---\napiVersion: \"networking.istio.io/v1alpha3\"\nkind: \"VirtualService\"\nmetadata:\n  creationTimestamp: \"2025-08-04T09:18:53Z\"\n  generation: 1\n  labels:\n    ihmp_app_name: \"llama2-7b-------3617\"\n    ihmp_instance_uuid: \"2361cdd3-0e3d-484f-b3ab-26c01c1ea5fe\"\n  managedFields:\n  - apiVersion: \"networking.istio.io/v1alpha3\"\n    fieldsType: \"FieldsV1\"\n    fieldsV1:\n      f:metadata:\n        f:labels:\n          .: {}\n          f:ihmp_app_name: {}\n          f:ihmp_instance_uuid: {}\n      f:spec:\n        .: {}\n        f:gateways: {}\n        f:hosts: {}\n        f:http: {}\n    manager: \"fabric8-kubernetes-client\"\n    operation: \"Update\"\n    time: \"2025-08-04T09:18:53Z\"\n  name: \"llama2-7b-------3617\"\n  namespace: \"1\"\n  resourceVersion: \"257419338\"\n  uid: \"9c3790e0-3576-4dfd-b462-4a4e16c12897\"\nspec:\n  gateways:\n  - \"edgemesh-gateway\"\n  hosts:\n  - \"llama2-7b-------3617.conn-infra.sail-cloud.com\"\n  http:\n  - match:\n    - port: 61103\n    route:\n    - destination:\n        host: \"llama2-7b-------3617\"\n        port:\n          number: 9300\n\n",
				"userId": "1",
				"tenantId": "1",
				"portsMappingJsonParse": [
					{
						"protocol": "TCP",
						"port": "9300"
					}
				],
				"environmentVarsJsonParse": {}
			},
			{
				"id": 63,
				"name": "nginx-web----0010",
				"instanceUuid": "773e77d7-4d07-4559-9acb-06dff05e5df1",
				"type": "APP",
				"machineId": 3,
				"externalId": 3,
				"imageAddress": "artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23",
				"cpuCores": 1,
				"cpuCode": "i9-11500k",
				"memorySize": 256,
				"gpuCode": "2684",
				"gpuNum": 1,
				"diskSize": 0,
				"imageCode": null,
				"resourceInfo": null,
				"sshAddress": null,
				"sshLoginUser": "root",
				"sshLoginPassword": null,
				"portsMapping": "[{\"protocol\": \"TCP\", \"port\": \"80\"}]",
				"environmentVars": "{}",
				"createdAt": "2025-08-06 09:40:20",
				"launchedAt": "2025-08-06 16:28:02",
				"terminatedAt": null,
				"terminatedType": null,
				"terminatedUser": null,
				"instanceStatus": 3,
				"metaData": "{\"port\":[61521],\"url\":\"nginx-web----0010.conn-infra.sail-cloud.com\"}",
				"yamlConf": "---\napiVersion: \"apps/v1\"\nkind: \"Deployment\"\nmetadata:\n  annotations:\n    actions: \"1/create/2025-08-06T01:40:20.660888303Z\"\n  labels:\n    is_newly: \"1\"\n    ihmp_instance_uuid: \"773e77d7-4d07-4559-9acb-06dff05e5df1\"\n    ihmp_app_name: \"nginx-web----0010\"\n  name: \"nginx-web----0010\"\n  namespace: \"1\"\nspec:\n  replicas: 1\n  revisionHistoryLimit: 100\n  selector:\n    matchLabels:\n      ihmp_app_name: \"nginx-web----0010\"\n  strategy:\n    rollingUpdate:\n      maxSurge: \"25%\"\n      maxUnavailable: \"25%\"\n    type: \"RollingUpdate\"\n  template:\n    metadata:\n      labels:\n        ihmp_app_name: \"nginx-web----0010\"\n        ihmp_instance_uuid: \"773e77d7-4d07-4559-9acb-06dff05e5df1\"\n    spec:\n      containers:\n      - args:\n        - null\n        image: \"artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23\"\n        imagePullPolicy: \"IfNotPresent\"\n        name: \"nginx-web----0010\"\n        ports:\n        - containerPort: 80\n        resources:\n          limits:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n          requests:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n      dnsPolicy: \"ClusterFirst\"\n      nodeName: \"scsp04856\"\n      restartPolicy: \"Always\"\n      schedulerName: \"default-scheduler\"\n\n---\napiVersion: \"v1\"\nkind: \"Service\"\nmetadata:\n  annotations:\n    prometheus.io/scrape: \"true\"\n  labels:\n    ihmp_instance_uuid: \"773e77d7-4d07-4559-9acb-06dff05e5df1\"\n    ihmp_app_name: \"nginx-web----0010\"\n  name: \"nginx-web----0010\"\n  namespace: \"1\"\nspec:\n  ports:\n  - name: \"um8wnd\"\n    port: 80\n    protocol: \"TCP\"\n  selector:\n    ihmp_app_name: \"nginx-web----0010\"\n  type: \"ClusterIP\"\n\n---\napiVersion: \"networking.istio.io/v1alpha3\"\nkind: \"VirtualService\"\nmetadata:\n  creationTimestamp: \"2025-08-06T01:40:22Z\"\n  generation: 1\n  labels:\n    ihmp_app_name: \"nginx-web----0010\"\n    ihmp_instance_uuid: \"773e77d7-4d07-4559-9acb-06dff05e5df1\"\n  managedFields:\n  - apiVersion: \"networking.istio.io/v1alpha3\"\n    fieldsType: \"FieldsV1\"\n    fieldsV1:\n      f:metadata:\n        f:labels:\n          .: {}\n          f:ihmp_app_name: {}\n          f:ihmp_instance_uuid: {}\n      f:spec:\n        .: {}\n        f:gateways: {}\n        f:hosts: {}\n        f:http: {}\n    manager: \"fabric8-kubernetes-client\"\n    operation: \"Update\"\n    time: \"2025-08-06T01:40:22Z\"\n  name: \"nginx-web----0010\"\n  namespace: \"1\"\n  resourceVersion: \"260511278\"\n  uid: \"e19965a4-e79a-440f-80b9-7f30b2db5801\"\nspec:\n  gateways:\n  - \"edgemesh-gateway\"\n  hosts:\n  - \"nginx-web----0010.conn-infra.sail-cloud.com\"\n  http:\n  - match:\n    - port: 61521\n    route:\n    - destination:\n        host: \"nginx-web----0010\"\n        port:\n          number: 80\n\n",
				"userId": "1",
				"tenantId": "1",
				"portsMappingJsonParse": [
					{
						"protocol": "TCP",
						"port": "80"
					}
				],
				"environmentVarsJsonParse": {}
			},
			{
				"id": 64,
				"name": "nginx-web----4210",
				"instanceUuid": "ad2456e6-bd16-4383-b2ed-097e1b7d5c50",
				"type": "APP",
				"machineId": 5,
				"externalId": 3,
				"imageAddress": "artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23",
				"cpuCores": 1,
				"cpuCode": "i9-11300k",
				"memorySize": 256,
				"gpuCode": "2204",
				"gpuNum": 1,
				"diskSize": 0,
				"imageCode": null,
				"resourceInfo": null,
				"sshAddress": null,
				"sshLoginUser": "root",
				"sshLoginPassword": null,
				"portsMapping": "[{\"protocol\": \"TCP\", \"port\": \"80\"}]",
				"environmentVars": "{}",
				"createdAt": "2025-08-06 09:44:54",
				"launchedAt": "2025-08-06 15:50:31",
				"terminatedAt": null,
				"terminatedType": null,
				"terminatedUser": null,
				"instanceStatus": 3,
				"metaData": "{\"port\":[61643],\"url\":\"nginx-web----4210.conn-infra.sail-cloud.com\"}",
				"yamlConf": "---\napiVersion: \"apps/v1\"\nkind: \"Deployment\"\nmetadata:\n  annotations:\n    actions: \"1/create/2025-08-06T01:44:54.737689556Z\"\n  labels:\n    is_newly: \"1\"\n    ihmp_instance_uuid: \"ad2456e6-bd16-4383-b2ed-097e1b7d5c50\"\n    ihmp_app_name: \"nginx-web----4210\"\n  name: \"nginx-web----4210\"\n  namespace: \"1\"\nspec:\n  replicas: 1\n  revisionHistoryLimit: 100\n  selector:\n    matchLabels:\n      ihmp_app_name: \"nginx-web----4210\"\n  strategy:\n    rollingUpdate:\n      maxSurge: \"25%\"\n      maxUnavailable: \"25%\"\n    type: \"RollingUpdate\"\n  template:\n    metadata:\n      labels:\n        ihmp_app_name: \"nginx-web----4210\"\n        ihmp_instance_uuid: \"ad2456e6-bd16-4383-b2ed-097e1b7d5c50\"\n    spec:\n      containers:\n      - args:\n        - null\n        image: \"artifacth-release.sail-cloud.com/tfai/ai-dlaas/nginx:1.23\"\n        imagePullPolicy: \"IfNotPresent\"\n        name: \"nginx-web----4210\"\n        ports:\n        - containerPort: 80\n        resources:\n          limits:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n          requests:\n            cpu: \"1\"\n            memory: \"256Gi\"\n            nvidia.com/gpu: \"1\"\n      dnsPolicy: \"ClusterFirst\"\n      nodeName: \"scsp04858\"\n      restartPolicy: \"Always\"\n      schedulerName: \"default-scheduler\"\n\n---\napiVersion: \"v1\"\nkind: \"Service\"\nmetadata:\n  annotations:\n    prometheus.io/scrape: \"true\"\n  labels:\n    ihmp_instance_uuid: \"ad2456e6-bd16-4383-b2ed-097e1b7d5c50\"\n    ihmp_app_name: \"nginx-web----4210\"\n  name: \"nginx-web----4210\"\n  namespace: \"1\"\nspec:\n  ports:\n  - name: \"yzwzfw\"\n    port: 80\n    protocol: \"TCP\"\n  selector:\n    ihmp_app_name: \"nginx-web----4210\"\n  type: \"ClusterIP\"\n\n---\napiVersion: \"networking.istio.io/v1alpha3\"\nkind: \"VirtualService\"\nmetadata:\n  creationTimestamp: \"2025-08-06T01:44:54Z\"\n  generation: 1\n  labels:\n    ihmp_app_name: \"nginx-web----4210\"\n    ihmp_instance_uuid: \"ad2456e6-bd16-4383-b2ed-097e1b7d5c50\"\n  managedFields:\n  - apiVersion: \"networking.istio.io/v1alpha3\"\n    fieldsType: \"FieldsV1\"\n    fieldsV1:\n      f:metadata:\n        f:labels:\n          .: {}\n          f:ihmp_app_name: {}\n          f:ihmp_instance_uuid: {}\n      f:spec:\n        .: {}\n        f:gateways: {}\n        f:hosts: {}\n        f:http: {}\n    manager: \"fabric8-kubernetes-client\"\n    operation: \"Update\"\n    time: \"2025-08-06T01:44:54Z\"\n  name: \"nginx-web----4210\"\n  namespace: \"1\"\n  resourceVersion: \"260517013\"\n  uid: \"c18c9cf1-96a6-4e54-9977-456ccc1d8816\"\nspec:\n  gateways:\n  - \"edgemesh-gateway\"\n  hosts:\n  - \"nginx-web----4210.conn-infra.sail-cloud.com\"\n  http:\n  - match:\n    - port: 61643\n    route:\n    - destination:\n        host: \"nginx-web----4210\"\n        port:\n          number: 80\n\n",
				"userId": "1",
				"tenantId": "1",
				"portsMappingJsonParse": [
					{
						"protocol": "TCP",
						"port": "80"
					}
				],
				"environmentVarsJsonParse": {}
			}
		],
		"total": 4,
		"size": 10,
		"current": 1,
		"orders": [],
		"optimizeCountSql": true,
		"searchCount": true,
		"maxLimit": null,
		"countId": null,
		"pages": 1
	}
}
```

* 失败(404)

```javascript
暂无数据
```

**Query**
